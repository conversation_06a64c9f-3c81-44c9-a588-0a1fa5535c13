---
date created: 星期三, 八月 20日 2025, 5:33:21 下午
date modified: 星期三, 八月 27日 2025, 9:54:33 上午
---
# 《我的同事，死于"健康打卡"》悬疑小说大纲（12章精简版）

## 一、整体结构概览

**核心悬念**：明星程序员陈阳猝死，"元力+"手环显示他死前状态完美，但真相是什么？

**三重反转线**：
1. 受害者假说（手环数据被公司控制，员工长时间疲劳工作，手环数据却显示良好，员工过劳死）→ 2. 公司谋杀假说（被蓄意清除）→ 3. 造物主死于造物（自己设计的系统杀死了自己）

**主要嫌疑人**：
- 安总监（HR总监）：动机-清除异己，手段-掌控系统，不在场-管理层身份
- 项目经理王磊：动机-嫉妒陈阳，手段-技术权限，不在场-出差记录
- 陈阳本人：动机-清除竞争者，手段-系统后门，不在场-已死（最终真凶）

## 二、详细章节大纲（12章结构）

### 第一幕：设置与钩子（第1-3章，0%-25%）

#### 第1章：异样的完美数据与猝死现场
**强钩引子+案发**
- 开场：凌晨3点，李默被急救车警笛声惊醒，从公司楼下经过
- 异样细节：手机推送"元力+"排行榜，陈阳依然排名第一，98分"精力充沛"
- 案发：陈阳在工位上猝死，法医鉴定"心源性猝死"
- 不安信号：李**默想起昨天陈阳苍白的脸色与手环显示的完美数据不符
- **线索植入1**：陈阳的心率曲线异常平滑（三明治法：夹在其他正常数据中）
- **线索植入2**：陈阳桌上有一张写着"v1.2"的便签纸
- **线索植入3**：李默发现陈阳最后登录的是测试服务器，而非生产环境  
**伏笔埋设**：**
- 安总监在现场的异常冷静，快速控制信息流传
- 陈阳的电脑被立即封存，理由是"保护商业机密"
#### 第2章：侦探入局与嫌疑圈建立
**李默的动机+嫌疑人设定+限制条件**
- 背景：李默曾因指出技术漏洞被上家公司"优化"，对大公司警惕
- 动机：李默与陈阳既是竞争关系又是知交好友，如果手环能及时提醒陈阳的身体异常状态，陈阳也不会就这么死了。李末总感觉陈阳的死另有隐情。
- 元力+项目介绍：与顶级保险公司合作，24小时健康监测
- 员工反应：有人质疑隐私，有人担心被监控，但迫于压力都佩戴
- 嫌疑方向：系统本身（技术故障）
- 封闭环境：公司启动危机公关，警方结案。
- **线索植入4**：安总监提到"达尔文计划"这个词，但立即转移话题
- **线索植入5**：李默发现陈阳的工作电脑被远程格式化  
- **线索植入6**：公司服务器新增了一个名为"达尔文"的数据库  
**人物深化**：
- 陈阳的完美主义：从不允许自己在任何指标上落后
- 安总监的专业冷血：用最温和的语言传达最残酷的现实
#### 第3章：首个误导与深入调查
- 警方通报，陈阳患有先天性的房间隔缺损，然后由于过度劳累，诱发了“心源性猝死”，属于意外死亡。
- 李默发现"元力+"系统有隐藏的管理员权限
- 发现安总监曾秘密调阅过陈阳的详细健康数据
- 利用维护权限查看服务器日志，发现陈阳死前一小时系统收到两套不同数据
- 决定深入调查：李默意识到这不是简单的猝死案例
- 初步推断：李默怀疑是安总监利用系统谋杀了陈阳
- 李末怀疑"元力+"系统伪造员工健康数据，压榨员工长期高强度工作。陈阳长期高强度工作极度劳累，而在最后心脏病发时，极度危险的数据也被伪造的正常健康数据代替，手环没能及时发出警报。
- 发现陈阳与安总监有过多次私下会面
- **线索植入6**：第一套数据显示极度危险，第二套数据完美正常
- **反转种子**：李默注意到安总监的数据调阅时间是在陈阳死后，应该不是安总监动的手

### 第二幕上半：试错与推进（第4-6章，25%-50%）

#### 第4章：红鲱鱼与第二现场

**王磊嫌疑+扩大调查**

- 发现王磊在陈阳死前曾尝试修改项目权限，承认嫉妒但声称出差

- 发现手环固件中的"诚信干预协议"，提到特定频率的次声波功能
- 推断：怀疑王磊是“达尔文计划”的执行者，是他利用手环杀了陈阳

- **线索植入7**：王磊的出差记录有时间漏洞（实际是系统延迟导致）

- **线索植入8**：技术文档显示次声波功能从未被激活过

- **立场大逆转：** 这不是过失杀人，这是**蓄意谋杀**。手环不是监测工具，是**行刑工具**。

#### 第5章：第二现场与小胜利

- 李默开始关注手环数据，发现同事们的"元力值"普遍偏高
- 发现近期有3名员工"因病离职"，离职前元力值都异常
- 第二现场：公司另一位员工因“元力值”长期不达标，绩效被评为C，在办公室情绪崩溃，砸坏了自己的手环，哭喊着“它在杀我”。这加剧了恐怖氛围，也让李默觉得自己的调查方向没错：**这个系统在杀人**。
- 与IT部门同事老刘暗中交流，了解手环系统的技术细节
- **更多线索**：手环硬件中确实存在次声波发生器
- 陈阳的数据记录中有大量"数据包重传"记录
- 手环固件版本在陈阳死前一天进行了更新

**调查遇阻**：
- 安总监约谈李默，警告他不要"传播谣言"，系统权限被收回
- 安总监无意中提到"陈阳的理想主义害了他"

#### 第6章：意外发现与立场逆转
调查受阻：
- 王磊主动配合调查，证明自己的清白，线索又断了。
- 李默的调查被发现，被怀疑"恶意传播公司机密"，面临开除威胁
- 同事们开始疏远李默，认为他"压力过大"
- 手环开始频繁提醒他"注意休息"
- 发现自己的系统访问记录可能暴露了调查行为  
意外发现：
- **线索植入**：王磊提到陈阳最近行为异常，经常深夜修改代码
-  李默开始调查陈阳的个人测试服务器，发现了一个加密文件夹。他利用陈阳的自恋性格和常用密码习惯，成功解密。**线索获得：** 发现了名为《净化论》的文档（二次加密，无法读取）和伪造生理数据的Python脚本。
- 李默成功在自己的测试环境中运行了陈阳的脚本，完美复现了“伪造数据”的过程。
- **立场逆转（得出结论）：** 陈阳为了维持“神话”形象，长期用脚本伪造健康数据，无视身体警报，最终被自己和这套内卷系统所杀。公司是间接凶手。
- 未解之谜：1.“达尔文计划”的具体内容究竟是什么？2.那个没有破解的“进化论文档”究竟藏着什么秘密。3.公司在这其中到底扮演着什么角色？  
危机：李末尝试破解《净化论》文档，结果出发了文档的自毁程序，48小时内不能破解，那文档就会自毁。

### 第二幕下半：反扑与困局（第7-9章，50%-75%）

#### 第7章  
李默向公司请了病假，准备将证据爆料给媒体，但还有疑问没有解决。  
遗留问题：1.“达尔文计划”的具体内容究竟是什么？  
2.那个没有破解的“进化论文档”究竟藏着什么秘密。  
3.公司在这其中到底扮演着什么角色？ 李默向公司申请了病假。 
- 解决所有问题的关键：破解《净化论》文档。 李默回到家打开文档，立刻弹出一个窗口，显示陈阳留下的话，大意是自己的身体最近越来越差，有不好的预感，你现在打开这个文档，那么说明我很有可能出了意外。我不知道你是谁，但我最讨厌无能者，如果想知道真相，那就解开我的谜题。窗口中显示开始倒计时，5分钟内如果没法解开谜题，文档就会自毁。真相永远沉没。 计时器开始计时。在正式的谜题展示之前，先问这个问题：“现在有一次决定是否需要上网搜索解题方法的机会，请你选择是或者否，一旦你选择否，我的程序立刻切断你的设备和网络之间的联系，你将只能凭自己的力量解决难题。” 李默不知道这个问题是什么意思，为什么要主动提供上网搜索解答方法的提示，是因为这个谜题是陈阳自创的，网上没法找到答案？李默选择相信自己，选择了否。 具体谜题：“四个死刑犯半夜从山顶的监狱里逃跑，狱警在后面追赶，现在前面只有一条路，横跨峡谷的古老吊桥。A犯人是年轻男性，B犯人是年轻女性，C犯人是个胖子，D犯人是个老头，A过桥需要5分钟，B过桥需要6分钟，C需要12分钟，D需要15分钟。糟糕的是，吊桥只能承受两个人的重量。而且夜里没有亮光，任何犯人过桥都必须持有提灯。提灯无法被抛掷，必须由人带过桥。现在，请找出让他们全部安全过桥的最短时间。你需要在倒计时结束前，将正确的时间（分钟数）输入下方的答案框。记住，机会只有一次。” 

#### 第8章：  

- 破解《净化论》文档。里面是一份个人笔记和"达尔文计划"的立项文档。
- 李默发现"达尔文计划"的立项文档，创始人是陈阳
- “达尔文计划”的内容是利用手环清除低效员工。
- 震惊发现：笔记记录，陈阳认为大部分同事是"伪奋斗者"，应被清除，整个诚信干预协议是陈阳主导设计的，陈阳的真实动机是用技术手段清除他眼中的"低效员工"

**线索收集**：
- 陈阳的个人笔记：对多名同事的"效率评估"和"清除优先级"  
重大反转：
- 陈阳不是受害者，而是这个杀人系统的设计者
- 他的《净化论》不是理论构想，而是实施指南
- 他伪造自己的数据是为了避免被自己的系统"误杀"  
**新的疑问**：那陈阳到底是怎么死的，为什么他最终还是被自己设计的系统所杀。

#### 第9章：至暗时刻
目前状况：  
- 感觉自己深陷危险，可能会成为下一个"猝死"案例
- 虽然真相还没有完全明了，但是手环还在运行，公司里的人生命都在受到威胁。
- 李默质疑自己的判断和动机
- 意识到揭露真相可能拯救其他人，但也会毁掉陈阳的名声。

重大决定：
- 不能等了，李默最终决定向媒体爆料真相，引发舆论风暴
- 公司股价暴跌，李默被起诉诽谤。  
反转：
- 公司拿出铁证，系统显示，元力值算法确实有问题，但“诚信干预协议”从未被启用，系统使用记录是无法被篡改的。
- 安瑞也在现场，李默观察到安瑞的表情有些不自然。
- 李默被停职

### 第三幕：重组与揭示（第10-12章，75%-100%）

#### 第10章：疑云

- 李默收到律师函。
- 在法律文件中，李默注意到系统版本，法律文件中系统还是V1.1版本，但李默记起在陈阳的桌面看到的"v1.2"的便签纸，回忆起老刘说陈阳死亡当日系统曾经更新过。
- 怀疑公司回退了系统版本，但正常回退的话还是会有系统记录的。
- 李默把老刘约出来，希望老刘能帮助自己，让自己查看公司的**宿主机（Host System）**，即运行所有系统的物理服务器上的底层记录。老刘拒绝了李默的要求，道出自己苦衷，想帮李默，但自己年纪大了，要养家，公司出问题的话自己的生计不好解决。

#### 第11章：真相大白
- 10天后，老刘突然联系李默。原来公司将整个IT硬件部门都解雇了（公司为了掩盖真相，消除证据），老刘的工作还是没有了。公司这种“卸磨杀驴”般冷酷无情的做法让他心寒和愤怒。同时他这几天一直受到良心的谴责。
- 老刘交给李默一个移动固态硬盘，里面是元力+操作系统服务器的物理磁盘镜像。
- 李默和老刘查看硬盘中的系统记录。发现关键信息，元力+系统曾经短暂的在一个测试容器中更新到了V1.2的测试版本，但后来整个测试容器都被删除了，容器中的测试记录也全没有了。 但物理服务器上的底层记录还留有，调取发现，V1.2测试版本仅对部分白名单设备生效；生产控制面“未启用”属实，但在部分白名单设备上短暂启用。
- 系统更新导致陈阳的伪造脚本出现延迟，真实的危险数据先到达，在1.2版本触发了"清除协议"。
- **手法揭示**：陈阳准备在测试容器里进行V1.2版本（启动清除协议）的测试，但他没料到版本的更新导致了自己的伪造脚本出现延迟，真实的危险数据先到达，触发了"清除协议"。
- 真相大白：造物主死于造物，完美的讽刺。  

#### 第12章：动机揭晓与余波主题
**完整真相+尾声反思**

- **线索闭环**：
- 李默整理所有证据，发现安总监一直知道真相但选择隐瞒
- 准备与安总监最终摊牌
- 搜寻证据，找到系统短时间更新过，但随后回退到1.1版本的证据。时间显示，公司于当晚命案发生后，回退过系统。"清除协议"是在1.2版本被触发的。
- 陈阳的完美主义和对"伪奋斗者"的鄙视，设计系统清除他眼中的"累赘"
- 证据闭环：动机（极端效率主义）+手段（清除协议）+机会（脚本失效）
- 李默选择公开真相，公司取消"元力+"计划
- **主题回响**：科技异化下的人性扭曲，内卷的终极代价
- **续作钩子**：李默发现其他公司也在使用类似系统

## 三、线索布置检查表（12章精简版）

### 公平性线索（至少出现两次）
1. **陈阳心率曲线异常平滑**（第1章植入，第3章验证，第9章解释）
2. **"v1.2"便签**（第1章植入，第6章发现脚本版本，第9章理解含义）
3. **测试服务器登录**（第1章发现，第6章深入调查）
4. **"达尔文计划"**（第2章提及，第8章发现文档，第11章揭示真相）
5. **双重数据**（第3章发现，第9章理解原因）

### 红鲱鱼线索
1. **安总监的数据调阅**（指向公司谋杀，实为事后调查）
2. **王磊的权限修改**（指向内部竞争，实为正常工作）
3. **系统从未激活**（指向技术故障，实为陈阳的隐藏功能）

### 真相线索
1. **《净化论》文档**（陈阳的极端思想）
2. **数据伪造脚本**（陈阳的作弊行为）
3. **系统更新时间**（导致脚本失效的关键）
4. **"诚信干预协议"**（陈阳设计的清除机制）

## 五、主题深化

**核心主题**：科技监控下的人性异化与内卷的终极代价  
**次要主题**：
- 完美主义的危险性
- 技术伦理的缺失  
- 职场竞争的扭曲
- 监控社会的隐患

**情感落点**：悲剧性的讽刺——最聪明的人死于自己的聪明，最完美的系统杀死了它的创造者。