{"recentFiles": [{"basename": "大纲12章", "path": "悬疑小说/大纲12章.md"}, {"basename": "神级提示词", "path": "小说prompt分类/神级提示词.md"}, {"basename": "大纲20章", "path": "悬疑小说/大纲20章.md"}, {"basename": "claude大纲13章", "path": "悬疑小说/claude大纲13章.md"}, {"basename": "悬疑小说大纲claude", "path": "小说prompt分类/自研提示词集合/悬疑小说大纲claude.md"}, {"basename": "悬疑小说文风", "path": "小说prompt分类/文风相关（攻克难题）/悬疑小说文风.md"}, {"basename": "悬疑小说大纲GPT5", "path": "小说prompt分类/自研提示词集合/悬疑小说大纲GPT5.md"}, {"basename": "悬疑小说大纲gemini", "path": "小说prompt分类/自研提示词集合/悬疑小说大纲gemini.md"}, {"basename": "GPT5优化", "path": "小说prompt分类/自研提示词集合/GPT5优化.md"}, {"basename": "deepseekV3悬疑小说大纲", "path": "小说prompt分类/自研提示词集合/deepseekV3悬疑小说大纲.md"}, {"basename": "claude优化", "path": "小说prompt分类/自研提示词集合/claude优化.md"}, {"basename": "古典雅致叙事风格模仿提示词", "path": "小说prompt分类/文风相关（攻克难题）/古典雅致叙事风格模仿提示词.md"}, {"basename": "赘婿风格提示词测试（这个是最新完成版本）", "path": "小说prompt分类/文风相关（攻克难题）/赘婿风格提示词测试（这个是最新完成版本）.md"}, {"basename": "chatgpt修改文风2", "path": "小说prompt分类/文风相关（攻克难题）/chatgpt修改文风2.md"}, {"basename": "文章风格分析器 v1.0(小七姐原版）", "path": "小说prompt分类/文风相关（攻克难题）/文章风格分析器 v1.0(小七姐原版）.md"}, {"basename": "知乎悬疑脑洞创作专家", "path": "小说prompt分类/自研提示词集合/知乎悬疑脑洞创作专家.md"}, {"basename": "李白风格提示词", "path": "小说prompt分类/文风相关（攻克难题）/李白风格提示词.md"}, {"basename": "赘婿风格分析一", "path": "小说prompt分类/文风相关（攻克难题）/赘婿风格分析一.md"}, {"basename": "李继刚优化版", "path": "小说prompt分类/文风相关（攻克难题）/李继刚优化版.md"}, {"basename": "暂存", "path": "小说prompt分类/文风相关（攻克难题）/暂存.md"}, {"basename": "文章风格分析器 v1.0 ", "path": "小说prompt分类/文风相关（攻克难题）/文章风格分析器 v1.0 .md"}, {"basename": "Lyra优化后的风格提示词", "path": "小说prompt分类/文风相关（攻克难题）/Lyra优化后的风格提示词.md"}, {"basename": "诡秘之主设定", "path": "小说prompt分类/文风相关（攻克难题）/诡秘之主设定.md"}, {"basename": "英雄之旅小七姐", "path": "小说prompt分类/英雄之旅小七姐.md"}, {"basename": "救猫咪猫叔", "path": "小说prompt分类/救猫咪猫叔.md"}, {"basename": "悬疑小说", "path": "悬疑小说/悬疑小说.md"}, {"basename": "悬疑脑洞创作提示词优化版", "path": "悬疑脑洞创作提示词优化版.md"}, {"basename": "春宵载酒小说设定", "path": "春宵载酒小说设定/春宵载酒小说设定.md"}, {"basename": "自研提示词集合", "path": "小说prompt分类/自研提示词集合/自研提示词集合.md"}, {"basename": "爽点设计提示词（猫叔）", "path": "小说prompt分类/爽点设计提示词（猫叔）.md"}, {"basename": "梗王作家", "path": "小说prompt分类/梗王作家.md"}, {"basename": "李继刚提示词药剂师", "path": "小说prompt分类/李继刚提示词药剂师.md"}, {"basename": "小七姐提示词优化", "path": "小说prompt分类/小七姐提示词优化.md"}, {"basename": "AI写小说-“雪花写作法”保姆级Prompt", "path": "小说prompt分类/AI写小说-“雪花写作法”保姆级Prompt.md"}, {"basename": "玄幻小说写作专家pormpt", "path": "春宵载酒小说设定/玄幻小说写作专家pormpt.md"}, {"basename": "金庸武侠短篇", "path": "小说prompt分类/金庸武侠短篇.md"}, {"basename": "短篇科幻小说作家", "path": "小说prompt分类/短篇科幻小说作家.md"}, {"basename": "claude神级思考提示词", "path": "小说prompt分类/claude神级思考提示词.md"}, {"basename": "AI写小说-“玄幻文”小说家完整Prompt-v2.0", "path": "小说prompt分类/AI写小说-“玄幻文”小说家完整Prompt-v2.0.md"}, {"basename": "十三月乐子文风（新梗王）", "path": "小说prompt分类/十三月乐子文风（新梗王）.md"}, {"basename": "AI写小说-“猫咪十五节拍”Prompt模板", "path": "小说prompt分类/AI写小说-“猫咪十五节拍”Prompt模板.md"}, {"basename": "大纲prompt（救猫咪）", "path": "小说prompt分类/大纲prompt（救猫咪）.md"}, {"basename": "AI写小说-“规则怪谈”小说家独家Prompt", "path": "小说prompt分类/AI写小说-“规则怪谈”小说家独家Prompt.md"}, {"basename": "八要素十序列大纲", "path": "小说prompt分类/八要素十序列大纲.md"}, {"basename": "毒蛇编辑（十三月）", "path": "小说prompt分类/毒蛇编辑（十三月）.md"}, {"basename": "AI写小说-“仙侠文”小说家独家Prompt", "path": "小说prompt分类/AI写小说-“仙侠文”小说家独家Prompt.md"}, {"basename": "好莱坞剧本导师", "path": "小说prompt分类/好莱坞剧本导师.md"}, {"basename": "创意生成器（猫叔）", "path": "小说prompt分类/创意生成器（猫叔）.md"}, {"basename": "知乎短篇小说导语生成2", "path": "小说prompt分类/知乎短篇小说导语生成2.md"}, {"basename": "知乎短篇小说导语生成", "path": "小说prompt分类/知乎短篇小说导语生成.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}